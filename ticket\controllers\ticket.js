import {inngest} from "../inngest/client.js";
import Ticket from "../models/ticket.js";

export const createTicket = async (req, res) => {
    try {
        const {title, description} = req.body;
        if(!title || !description){
            return res.status(400).json({error: "Title and description are required"});
        }
        const newTicket = await Ticket.create({
            title,
            description,
            createdBy: req.user._id.toString(),
        })
        await inngest.send({
            name: "ticket/created",
            data: {ticketId: newTicket._id.toString(),
                title,
                description,
                createdBy: req.user._id.toString(),
            }
        });
        return res.status(201).json({
            message: "Ticket created successfully",
            ticket: newTicket});
    } catch (error) {
        console.error("Error creating ticket", error.message)
        return res.status(500).json({error: "Internal server error"});
    }
}

export const getTickets = async (req, res) => {
    try{
        const user = req.user;
        let tickets = [];
        if(user.role !== "user"){
            tickets = await Ticket.find({}).populate("assignedTo", ["email" , "_id"])
            .sort({createdAt: -1});
        }else {
            tickets = await Ticket.find({createdBy: user._id})
            .select("title description createdAt status")
            .sort({createdAt: -1});
        }
        return res.status(200).json({tickets});
    }catch(error){
        console.error("Error getting tickets", error.message)
        return res.status(500).json({error: "Internal server error"});
    }
}

export const getTicket = async (req, res) => {
    try {
        const user = req.user;
        let ticket;

        if(user.role !== "user"){
            ticket = await Ticket.findById(req.params.id).populate("assignedTo", ["email" , "_id"])
            .populate("createdBy", ["email" , "_id"]);
        }else{
            ticket = await Ticket.findOne({
                createdBy: user._id,
                _id: req.params.id,
            }).select("title description createdAt status");
        }

        if(!ticket){
            return res.status(404).json({error: "Ticket not found"});
        }

        return res.status(200).json({ticket});
    } catch (error) {
        console.error("Error fetch ticket", error.message);
        return res.status(500).json({error: "Internal server error"});
    }
}