import { useState } from "react"
import { useNavigate, <PERSON> } from "react-router-dom"

function Signup() {

  const [form , setForm] = useState({
    email: "",
    password: "",
    confirm: ""
  })
  const[loading , setLoading] = useState(false)
  const navigate = useNavigate()

  const handleChange = (e) => {
    setForm({
      ...form,
      [e.target.name]: e.target.value
    })
  }
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    try{
      const res = await fetch(`${import.meta.env.VITE_SERVER_URL}/auth/signup`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(form)
      })
      const data = await res.json()
      if(res.ok){
        localStorage.setItem("token", data.token)
        localStorage.setItem("user", JSON.stringify(data.user))
        navigate("/")
      }else{
        alert(data.message || "Signup failed")
      }
    }catch(error){
      alert("Signup - Something went wrong")
    }
    finally{
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-base-200">
      <div className="card w-full max-w-sm shadow-xl bg-base-100">
        <form onSubmit={handleSubmit} className="card-body">
        <h2 className="card-title justify-center">Sign Up</h2>

        <input 
        type="email"
        name="email"
        value={form.email}
        onChange={handleChange}
        placeholder="Email"
        className="input input-bordered w-full max-w-xs"
        required 
        />

        <input 
        type="password"
        name="password"
        value={form.password}
        onChange={handleChange}
        placeholder="Password"
        className="input input-bordered w-full max-w-xs"
        required 
        />

    <div>
      <button type="submit"
      className="btn btn-primary w-full max-w-xs"
      disabled={loading}
      >
        {loading ? "Signing up..." : "Sign Up"}
      </button>
    </div>

    <div className="text-center mt-4">
      <p>Already have an account? <Link to="/login" className="link link-primary">Login</Link></p>
    </div>

        </form>
      </div>
    </div>
  )
}

export default Signup
