import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import { BrowserRouter, Route, Routes } from 'react-router-dom'
import CheckAuth from './components/check-auth.jsx'
import Tickets from './pages/tickets.jsx'
import TicketDetailsPage from './pages/ticket.jsx'
import Admin from './pages/admin.jsx'
import Login from './pages/login.jsx'
import Signup from './pages/signup.jsx'

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <BrowserRouter>
      <Routes>
        <Route path="/" 
        element=
        {<CheckAuth protected={true}>
          <Tickets/>
        </CheckAuth>} />

        <Route path="/tickets/:id"
         element={
          <CheckAuth protected={true}>
            <TicketDetailsPage/>
          </CheckAuth>
         }
         />

         <Route 
         path="/admin"
         element={
          <CheckAuth protected={true} admin={true}>
            <Admin/>
          </CheckAuth>
         }
         />

         <Route
         path="/login" element={
          <CheckAuth protected={false}>
            <Login/>
          </CheckAuth>
         }
         />

         <Route
         path="/signup" element={
          <CheckAuth protected={false}>
            <Signup/>
          </CheckAuth>
         }
         />
        </Routes>
    </BrowserRouter>
  </StrictMode>,
)
