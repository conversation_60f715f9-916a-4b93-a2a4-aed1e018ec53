import { useEffect, useState } from "react"


function Admin() {
    const [users, setUsers] = useState([])
    const [filteredUsers, setFilteredUsers] = useState([])
    const [editingUser, setEditingUser] = useState(null)
    const [formData, setFormData] = useState({
        role: "",
        skills: ""
    });
    const [searchQuery, setSearchQuery] = useState("")

    const token = localStorage.getItem("token")

    useEffect(() => {
        fetchUsers()
    }, []);

    const fetchUsers = async () => {
        try{
            const res = await fetch(`${import.meta.env.VITE_SERVER_URL}/auth/get-users`, {
                headers: {
                    "Authorization": `Bearer ${token}`
                }
            });
            const data = await res.json();
            if(res.ok){
                setUsers(data);
                setFilteredUsers(data)
            }else{
                console.error(data.error)
            }
        }catch(err){
            console.error("Error fetching users", err)
        }
    };

    const handleEditClick = (user) => {
        setEditingUser(user.email);
        setFormData({
            role: user.role,
            skills: user.skills.join(",")
        })
    }

    const handleUpdate = async () => {
        try{
            const res = await fetch(`${import.meta.env.VITE_SERVER_URL}/auth/update-user`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`
                },
                body: JSON.stringify({
                    email: editingUser,
                    role: formData.role,
                    skills: formData.skills.split(",").map((skill) => skill.trim()).filter(Boolean)
                })
            })
            const data = await res.json();
            if(res.ok){
                setEditingUser(null);
                setFormData({role: "" , skills: ""});
                fetchUsers(); // Refresh the user list
            } else {
                console.error(data.error || "Failed to update user")
            }
        }catch(err){
            console.error("Error updating user", err)
        }
    };

    const handleSearch = (e) => {
        const query = e.target.value.toLowerCase();
        setSearchQuery(query);
        setFilteredUsers(
            users.filter((user) => user.email.toLowerCase().includes(query))
        )
    }
    
  return (
      <div className="max-w-4xl mx-auto mt-10">
      <h1 className="text-2xl font-bold mb-6">Admin Panel </h1>

      <input 
      type="text"
      placeholder="Search users"
      value={searchQuery}
      onChange={handleSearch}
      className="input input-bordered w-full mb-6"
      
      />

      {filteredUsers.map((user) => (
        <div
        key={user._id} 
        className="bg-base-100 shadow rounded p-4 mb-4 border"
        >
            <p>
         <strong>Email: </strong>
                {user.email}
            </p>
            <p>
                <strong>Role: </strong>
                {user.role}
            </p>
            <p>
                <strong>Skills: </strong>
                {user.skills && user.skills.length ? user.skills.join(", ") : "N/A"}
            </p>

        {editingUser === user.email ? (
            <div className="mt-4 space-y-2">
                <select className="select select-bordered w-full max-w-xs"
                value={formData.role}
                onChange={(e) => setFormData({...formData, role: e.target.value})}
                >
                    <option value="user">User</option>
                    <option value="moderator">Moderator</option>
                    <option value="admin">Admin</option>

                </select>
                <input
                type="text"
                placeholder="Skills (comma separated)"
                value={formData.skills}
                onChange={(e) => setFormData({...formData, skills: e.target.value})}
                className="input input-bordered w-full max-w-xs"
                />

                <div className="flex gap-2">
                    <button className="btn btn-success btn-sm"
                    onClick={handleUpdate}
                    >
                        save
                    </button>
                    <button className="btn btn-error btn-sm"
                    onClick={() => setEditingUser(null)}>
                        cancel
                    </button>
                </div>
            </div>
        ): (
            <button className="btn btn-primary btn-sm mt-4"
            onClick={() => handleEditClick(user)}>
                Edit
            </button>
        )}
        </div>
      ))}
    </div>
  )
}

export default Admin
