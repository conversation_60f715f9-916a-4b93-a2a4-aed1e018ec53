import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import User from "../models/user.js";
import {inngest} from "../inngest/client.js";

export const signup = async (req, res) => {
    const {email , password, skills= []} = req.body;

    try{
        const hashed = await bcrypt.hash(password , 10);
        const user = await User.create({email , password: hashed , skills});

        //fire inngest  event
        await inngest.send({
            name: "user/signup",
            data: {email}
        });
        const token = jwt.sign({
            _id:user._id, role:user.role
        }, process.env.JWT_SECRET, {expiresIn: "1h"});
        res.json({user, token})
        
    }catch(error){
        res.status(500).json({error: "Signup failed" , detail: error.message});
    }
};

export const login = async (req, res) => {
    const {email , password} = req.body;
    try {
       const user = await User.findOne({email})
       if(!user) return res.status(401).json({error: "User not found"});

       const isMatch = await bcrypt.compare(password, user.password);

       if(!isMatch) {
        return res.status(401).json({error: "Invalid password"});
       }

       const token = jwt.sign(
        {
            _id:user._id, role:user.role
        },
        process.env.JWT_SECRET,
        {expiresIn: "1h"}
       )

       res.json({user, token})
    } catch (error) {
        res.status(500).json({error: "Login failed" , detail: error.message});
    }
}

export const logout = async (req, res) => {
    try {
        // For JWT logout, we typically just return success
        // The client should remove the token from localStorage
        res.json({message: "Logout successful"});
    } catch (error) {
        res.status(500).json({error: "Logout failed" , detail: error.message});
    }
}

export const updateUser = async (req, res) => {
    const {skills = [], role, email} = req.body
    try {
        if(req.user.role !== "admin"){
            return res.status(403).json({error: "Forbidden"});
        }
        const user = await User.findOne({email});
        if(!user){
            return res.status(404).json({error: "User not found"});
        }
    
        await User.updateOne(
            {email},
            {skills: skills.length ? skills : user.skills, role}
        );
        return  res.json({user});
    } catch (error) {
        res.status(500).json({error: "Update failed" , detail: error.message});
    }
}

export const getUsers = async (req, res) => {
    try{
        if(req.user.role !== "admin"){
            return res.status(403).json({error: "Forbidden"});
        }
        const users = await User.find().select("-password");
        return res.json({users});
    }catch(error){
        res.status(500).json({error: "Get users failed" , detail: error.message});
    }
}

