import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

function CheckAuth ({children, protected: protectedRoute, admin}) {
    const navigate = useNavigate()
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        const token = localStorage.getItem('token')
        const user = JSON.parse(localStorage.getItem('user') || '{}')

        if(protectedRoute){
            if(!token){
                navigate('/login')
            }else if(admin && user.role !== 'admin' && user.role !== 'moderator'){
                navigate('/')
            }else{
                setLoading(false)
            }
        }else{
            if(token){
                navigate('/')
            }else{
                setLoading(false)
            }
        }
    },[navigate, protectedRoute, admin])
  if(loading){
    return <div>
        Loading...
    </div>
  }
  return children;
}

export default CheckAuth
