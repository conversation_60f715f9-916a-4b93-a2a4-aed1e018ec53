import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import Navbar from './navbar.jsx'

function CheckAuth ({children, protected: protectedRoute, admin}) {
    const navigate = useNavigate()
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        const token = localStorage.getItem('token')
        const user = JSON.parse(localStorage.getItem('user') || '{}')



        if(protectedRoute){
            if(!token){
                navigate('/login')
            }else if(admin && user.role !== 'admin' && user.role !== 'moderator'){
                navigate('/')
            }else{
                setLoading(false)
            }
        }else{
            if(token){
                navigate('/')
            }else{
                setLoading(false)
            }
        }
    },[navigate, protectedRoute, admin])
  if(loading){
    return <div>
        <Navbar />
        <div className="flex items-center justify-center min-h-screen">
            <div className="loading loading-spinner loading-lg"></div>
        </div>
    </div>
  }
  return (
    <div>
      <Navbar />
      {children}
    </div>
  );
}

export default CheckAuth
