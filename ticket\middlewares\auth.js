import jwt from "jsonwebtoken"

export const authenticate = (req, res, next) => {
    const authHeader = req.headers.authorization;
    if(!authHeader) {
        return res.status(401).json({error: "Access Denied. No token provided."});
    }

    const token = authHeader.split(" ")[1];
    if(!token) {
        return res.status(401).json({error: "Access Denied. Invalid token format."});
    }

try{
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
}catch(error){
    res.status(401).json({error: "Invalid token."});
}

}